package com.bxm.customer.domain.query.valueAdded;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 查询增值交付单的查询参数
 *
 * 说明：所有查询条件均为可选，最终条件由 Service 层进行动态拼接
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("增值交付单查询参数")
public class DeliveryOrderQuery extends BaseVO {

    @ApiModelProperty("交付单编号")
    private String deliveryOrderNo;

    @ApiModelProperty("客户企业名称（模糊匹配）")
    private String customerName;

    @ApiModelProperty("统一社会信用代码")
    private String creditCode;

    @ApiModelProperty("税号")
    private String taxNo;

    @ApiModelProperty(value = "纳税性质，1-小规模纳税人，2-一般纳税人")
    private Integer taxpayerType;

    @ApiModelProperty(value = "增值事项类型ID")
    private Integer valueAddedItemTypeId;

    @ApiModelProperty(value = "账期开始（YYYYMM）")
    private Integer accountingPeriodStart;

    @ApiModelProperty(value = "账期结束（YYYYMM）")
    private Integer accountingPeriodEnd;

    @ApiModelProperty("交付状态")
    private String status;

    @ApiModelProperty("发起部门ID")
    private Long initiateDeptId;

    @ApiModelProperty("业务部门id")
    private Long businessDeptId;

    @ApiModelProperty("顶级业务部门id")
    private Long businessTopDeptId;

    @ApiModelProperty(value = "会计部门ID，-1表示未分派")
    private Long accountingDeptId;

    @ApiModelProperty("发起人用户ID")
    private Long createUid;

    @ApiModelProperty(value = "DDL开始日期（含）yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate ddlStart;

    @ApiModelProperty(value = "DDL结束日期（含）yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate ddlEnd;
}
